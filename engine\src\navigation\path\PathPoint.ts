/**
 * 路径点类
 */
import * as THREE from 'three';
import { PathTrigger } from '../types';
import { UUID } from '../../utils/UUID';

/**
 * 路径点类
 */
export class PathPoint {
  /** 路径点ID */
  public readonly id: string;
  /** 位置 */
  public position: THREE.Vector3;
  /** 停留时间（秒） */
  public waitTime: number;
  /** 行走速度（米/秒） */
  public speed: number;
  /** 动画名称 */
  public animation: string;
  /** 朝向目标 */
  public lookAt?: THREE.Vector3;
  /** 触发器列表 */
  public triggers: PathTrigger[];
  /** 自定义数据 */
  public userData: any;

  /**
   * 构造函数
   * @param position 位置
   * @param options 选项
   */
  constructor(
    position: THREE.Vector3,
    options: {
      id?: string;
      waitTime?: number;
      speed?: number;
      animation?: string;
      lookAt?: THREE.Vector3;
      triggers?: PathTrigger[];
      userData?: any;
    } = {}
  ) {
    this.id = options.id || UUID.generate();
    this.position = position.clone();
    this.waitTime = options.waitTime || 0;
    this.speed = options.speed || 1.0;
    this.animation = options.animation || 'walk';
    this.lookAt = options.lookAt?.clone();
    this.triggers = options.triggers || [];
    this.userData = options.userData || {};
  }

  /**
   * 克隆路径点
   * @returns 新的路径点实例
   */
  public clone(): PathPoint {
    return new PathPoint(this.position, {
      id: UUID.generate(), // 生成新的ID
      waitTime: this.waitTime,
      speed: this.speed,
      animation: this.animation,
      lookAt: this.lookAt,
      triggers: this.triggers.map(trigger => ({ ...trigger })),
      userData: { ...this.userData }
    });
  }

  /**
   * 设置位置
   * @param position 新位置
   */
  public setPosition(position: THREE.Vector3): void {
    this.position.copy(position);
  }

  /**
   * 设置朝向目标
   * @param target 朝向目标
   */
  public setLookAt(target: THREE.Vector3): void {
    this.lookAt = target.clone();
  }

  /**
   * 添加触发器
   * @param trigger 触发器
   */
  public addTrigger(trigger: PathTrigger): void {
    this.triggers.push(trigger);
  }

  /**
   * 移除触发器
   * @param index 触发器索引
   */
  public removeTrigger(index: number): void {
    if (index >= 0 && index < this.triggers.length) {
      this.triggers.splice(index, 1);
    }
  }

  /**
   * 清空触发器
   */
  public clearTriggers(): void {
    this.triggers.length = 0;
  }

  /**
   * 获取指定类型的触发器
   * @param type 触发器类型
   * @returns 触发器列表
   */
  public getTriggersByType(type: string): PathTrigger[] {
    return this.triggers.filter(trigger => trigger.type === type);
  }

  /**
   * 计算到另一个路径点的距离
   * @param other 另一个路径点
   * @returns 距离
   */
  public distanceTo(other: PathPoint): number {
    return this.position.distanceTo(other.position);
  }

  /**
   * 计算到指定位置的距离
   * @param position 位置
   * @returns 距离
   */
  public distanceToPosition(position: THREE.Vector3): number {
    return this.position.distanceTo(position);
  }

  /**
   * 序列化为JSON
   * @returns JSON对象
   */
  public toJSON(): any {
    return {
      id: this.id,
      position: {
        x: this.position.x,
        y: this.position.y,
        z: this.position.z
      },
      waitTime: this.waitTime,
      speed: this.speed,
      animation: this.animation,
      lookAt: this.lookAt ? {
        x: this.lookAt.x,
        y: this.lookAt.y,
        z: this.lookAt.z
      } : undefined,
      triggers: this.triggers,
      userData: this.userData
    };
  }

  /**
   * 从JSON反序列化
   * @param json JSON对象
   * @returns 路径点实例
   */
  public static fromJSON(json: any): PathPoint {
    const position = new THREE.Vector3(json.position.x, json.position.y, json.position.z);
    const lookAt = json.lookAt ? new THREE.Vector3(json.lookAt.x, json.lookAt.y, json.lookAt.z) : undefined;

    return new PathPoint(position, {
      id: json.id,
      waitTime: json.waitTime,
      speed: json.speed,
      animation: json.animation,
      lookAt: lookAt,
      triggers: json.triggers || [],
      userData: json.userData || {}
    });
  }

  /**
   * 验证路径点数据
   * @returns 验证结果
   */
  public validate(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证位置
    if (!this.position || isNaN(this.position.x) || isNaN(this.position.y) || isNaN(this.position.z)) {
      errors.push('位置数据无效');
    }

    // 验证速度
    if (this.speed <= 0) {
      errors.push('速度必须大于0');
    }

    // 验证等待时间
    if (this.waitTime < 0) {
      errors.push('等待时间不能为负数');
    }

    // 验证动画名称
    if (!this.animation || this.animation.trim() === '') {
      errors.push('动画名称不能为空');
    }

    // 验证朝向目标
    if (this.lookAt && (isNaN(this.lookAt.x) || isNaN(this.lookAt.y) || isNaN(this.lookAt.z))) {
      errors.push('朝向目标数据无效');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取路径点的字符串表示
   * @returns 字符串表示
   */
  public toString(): string {
    return `PathPoint(${this.id}, pos: ${this.position.x.toFixed(2)}, ${this.position.y.toFixed(2)}, ${this.position.z.toFixed(2)}, speed: ${this.speed}, wait: ${this.waitTime})`;
  }
}
