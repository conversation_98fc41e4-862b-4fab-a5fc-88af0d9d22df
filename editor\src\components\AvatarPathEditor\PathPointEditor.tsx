/**
 * 路径点编辑器组件
 */
import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Space,
  Divider,
  Card,
  List,
  Modal,
  message,
  Tooltip,
  Tag,
  Collapse,
  Switch
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;

/**
 * 路径点数据接口
 */
interface PathPointData {
  id: string;
  position: { x: number; y: number; z: number };
  waitTime: number;
  speed: number;
  animation: string;
  lookAt?: { x: number; y: number; z: number };
  triggers?: PathTriggerData[];
  userData?: any;
}

/**
 * 路径触发器数据接口
 */
interface PathTriggerData {
  type: 'dialogue' | 'animation' | 'sound' | 'event' | 'custom';
  data: any;
  condition?: string;
  delay?: number;
  once?: boolean;
}

/**
 * 编辑器属性
 */
interface PathPointEditorProps {
  /** 路径点数据 */
  point: PathPointData;
  /** 是否只读 */
  readonly?: boolean;
  /** 变化回调 */
  onChange?: (point: Partial<PathPointData>) => void;
  /** 关闭回调 */
  onClose?: () => void;
}

/**
 * 路径点编辑器组件
 */
export const PathPointEditor: React.FC<PathPointEditorProps> = ({
  point,
  readonly = false,
  onChange,
  onClose
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [showTriggerEditor, setShowTriggerEditor] = useState(false);
  const [editingTriggerIndex, setEditingTriggerIndex] = useState(-1);
  const [triggerForm] = Form.useForm();

  // 动画选项
  const animationOptions = [
    'idle',
    'walk',
    'run',
    'jump',
    'wave',
    'talk',
    'sit',
    'stand',
    'dance',
    'custom'
  ];

  // 触发器类型选项
  const triggerTypeOptions = [
    { value: 'dialogue', label: t('pathPoint.trigger.dialogue') },
    { value: 'animation', label: t('pathPoint.trigger.animation') },
    { value: 'sound', label: t('pathPoint.trigger.sound') },
    { value: 'event', label: t('pathPoint.trigger.event') },
    { value: 'custom', label: t('pathPoint.trigger.custom') }
  ];

  /**
   * 处理表单值变化
   */
  const handleFormChange = (changedValues: any, allValues: any) => {
    if (readonly) return;
    onChange?.(allValues);
  };

  /**
   * 添加触发器
   */
  const handleAddTrigger = () => {
    setEditingTriggerIndex(-1);
    triggerForm.resetFields();
    setShowTriggerEditor(true);
  };

  /**
   * 编辑触发器
   */
  const handleEditTrigger = (index: number) => {
    const trigger = point.triggers?.[index];
    if (trigger) {
      setEditingTriggerIndex(index);
      triggerForm.setFieldsValue(trigger);
      setShowTriggerEditor(true);
    }
  };

  /**
   * 删除触发器
   */
  const handleDeleteTrigger = (index: number) => {
    if (readonly) return;

    const newTriggers = [...(point.triggers || [])];
    newTriggers.splice(index, 1);
    onChange?.({ triggers: newTriggers });
    message.success(t('pathPoint.trigger.deleted'));
  };

  /**
   * 保存触发器
   */
  const handleSaveTrigger = () => {
    triggerForm.validateFields().then(values => {
      const newTriggers = [...(point.triggers || [])];
      
      if (editingTriggerIndex >= 0) {
        // 编辑现有触发器
        newTriggers[editingTriggerIndex] = values;
        message.success(t('pathPoint.trigger.updated'));
      } else {
        // 添加新触发器
        newTriggers.push(values);
        message.success(t('pathPoint.trigger.added'));
      }

      onChange?.({ triggers: newTriggers });
      setShowTriggerEditor(false);
    });
  };

  /**
   * 重置表单
   */
  const handleReset = () => {
    form.setFieldsValue(point);
  };

  /**
   * 渲染触发器数据编辑器
   */
  const renderTriggerDataEditor = (type: string) => {
    switch (type) {
      case 'dialogue':
        return (
          <>
            <Form.Item
              name={['data', 'text']}
              label={t('pathPoint.trigger.dialogueText')}
              rules={[{ required: true, message: t('pathPoint.trigger.textRequired') }]}
            >
              <TextArea rows={3} placeholder={t('pathPoint.trigger.textPlaceholder')} />
            </Form.Item>
            <Form.Item
              name={['data', 'speaker']}
              label={t('pathPoint.trigger.speaker')}
            >
              <Input placeholder={t('pathPoint.trigger.speakerPlaceholder')} />
            </Form.Item>
            <Form.Item
              name={['data', 'duration']}
              label={t('pathPoint.trigger.duration')}
            >
              <InputNumber min={0} step={0.1} addonAfter="s" />
            </Form.Item>
          </>
        );

      case 'animation':
        return (
          <>
            <Form.Item
              name={['data', 'animationName']}
              label={t('pathPoint.trigger.animationName')}
              rules={[{ required: true, message: t('pathPoint.trigger.animationRequired') }]}
            >
              <Select placeholder={t('pathPoint.trigger.selectAnimation')}>
                {animationOptions.map(anim => (
                  <Option key={anim} value={anim}>{anim}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              name={['data', 'loop']}
              label={t('pathPoint.trigger.loop')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
            <Form.Item
              name={['data', 'speed']}
              label={t('pathPoint.trigger.animationSpeed')}
            >
              <InputNumber min={0.1} max={5} step={0.1} defaultValue={1} />
            </Form.Item>
          </>
        );

      case 'sound':
        return (
          <>
            <Form.Item
              name={['data', 'soundUrl']}
              label={t('pathPoint.trigger.soundUrl')}
              rules={[{ required: true, message: t('pathPoint.trigger.soundRequired') }]}
            >
              <Input placeholder={t('pathPoint.trigger.soundPlaceholder')} />
            </Form.Item>
            <Form.Item
              name={['data', 'volume']}
              label={t('pathPoint.trigger.volume')}
            >
              <InputNumber min={0} max={1} step={0.1} defaultValue={1} />
            </Form.Item>
            <Form.Item
              name={['data', 'loop']}
              label={t('pathPoint.trigger.loop')}
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </>
        );

      case 'event':
        return (
          <>
            <Form.Item
              name={['data', 'eventName']}
              label={t('pathPoint.trigger.eventName')}
              rules={[{ required: true, message: t('pathPoint.trigger.eventRequired') }]}
            >
              <Input placeholder={t('pathPoint.trigger.eventPlaceholder')} />
            </Form.Item>
            <Form.Item
              name={['data', 'eventData']}
              label={t('pathPoint.trigger.eventData')}
            >
              <TextArea rows={3} placeholder={t('pathPoint.trigger.eventDataPlaceholder')} />
            </Form.Item>
          </>
        );

      case 'custom':
        return (
          <Form.Item
            name={['data']}
            label={t('pathPoint.trigger.customData')}
          >
            <TextArea rows={5} placeholder={t('pathPoint.trigger.customDataPlaceholder')} />
          </Form.Item>
        );

      default:
        return null;
    }
  };

  // 初始化表单
  useEffect(() => {
    form.setFieldsValue(point);
  }, [point, form]);

  return (
    <div className="path-point-editor">
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        disabled={readonly}
      >
        {/* 基本信息 */}
        <Card title={t('pathPoint.basicInfo')} size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name="id"
            label={t('pathPoint.id')}
          >
            <Input disabled />
          </Form.Item>

          {/* 位置 */}
          <Form.Item label={t('pathPoint.position')}>
            <Space>
              <Form.Item name={['position', 'x']} noStyle>
                <InputNumber placeholder="X" style={{ width: 80 }} />
              </Form.Item>
              <Form.Item name={['position', 'y']} noStyle>
                <InputNumber placeholder="Y" style={{ width: 80 }} />
              </Form.Item>
              <Form.Item name={['position', 'z']} noStyle>
                <InputNumber placeholder="Z" style={{ width: 80 }} />
              </Form.Item>
            </Space>
          </Form.Item>

          {/* 朝向目标 */}
          <Form.Item label={t('pathPoint.lookAt')}>
            <Space>
              <Form.Item name={['lookAt', 'x']} noStyle>
                <InputNumber placeholder="X" style={{ width: 80 }} />
              </Form.Item>
              <Form.Item name={['lookAt', 'y']} noStyle>
                <InputNumber placeholder="Y" style={{ width: 80 }} />
              </Form.Item>
              <Form.Item name={['lookAt', 'z']} noStyle>
                <InputNumber placeholder="Z" style={{ width: 80 }} />
              </Form.Item>
            </Space>
          </Form.Item>
        </Card>

        {/* 行为设置 */}
        <Card title={t('pathPoint.behavior')} size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name="waitTime"
            label={t('pathPoint.waitTime')}
            tooltip={t('pathPoint.waitTimeTooltip')}
          >
            <InputNumber min={0} step={0.1} addonAfter="s" />
          </Form.Item>

          <Form.Item
            name="speed"
            label={t('pathPoint.speed')}
            tooltip={t('pathPoint.speedTooltip')}
          >
            <InputNumber min={0.1} max={20} step={0.1} addonAfter="m/s" />
          </Form.Item>

          <Form.Item
            name="animation"
            label={t('pathPoint.animation')}
            tooltip={t('pathPoint.animationTooltip')}
          >
            <Select placeholder={t('pathPoint.selectAnimation')}>
              {animationOptions.map(anim => (
                <Option key={anim} value={anim}>{anim}</Option>
              ))}
            </Select>
          </Form.Item>
        </Card>

        {/* 触发器 */}
        <Card 
          title={t('pathPoint.triggers')} 
          size="small"
          extra={
            !readonly && (
              <Button
                type="primary"
                size="small"
                icon={<PlusOutlined />}
                onClick={handleAddTrigger}
              >
                {t('pathPoint.addTrigger')}
              </Button>
            )
          }
        >
          {point.triggers && point.triggers.length > 0 ? (
            <List
              size="small"
              dataSource={point.triggers}
              renderItem={(trigger, index) => (
                <List.Item
                  actions={
                    !readonly ? [
                      <Button
                        type="text"
                        size="small"
                        icon={<EditOutlined />}
                        onClick={() => handleEditTrigger(index)}
                      />,
                      <Button
                        type="text"
                        size="small"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => handleDeleteTrigger(index)}
                      />
                    ] : []
                  }
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <Tag color="blue">{trigger.type}</Tag>
                        {trigger.once && <Tag color="orange">{t('pathPoint.trigger.once')}</Tag>}
                        {trigger.delay && <Tag color="green">{trigger.delay}s</Tag>}
                      </Space>
                    }
                    description={
                      trigger.condition || t('pathPoint.trigger.noCondition')
                    }
                  />
                </List.Item>
              )}
            />
          ) : (
            <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
              {t('pathPoint.noTriggers')}
            </div>
          )}
        </Card>
      </Form>

      {/* 操作按钮 */}
      {!readonly && (
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Space>
            <Button onClick={handleReset} icon={<ReloadOutlined />}>
              {t('pathPoint.reset')}
            </Button>
            <Button onClick={onClose}>
              {t('pathPoint.close')}
            </Button>
          </Space>
        </div>
      )}

      {/* 触发器编辑器模态框 */}
      <Modal
        title={
          editingTriggerIndex >= 0 
            ? t('pathPoint.editTrigger') 
            : t('pathPoint.addTrigger')
        }
        open={showTriggerEditor}
        onOk={handleSaveTrigger}
        onCancel={() => setShowTriggerEditor(false)}
        width={600}
        okText={t('pathPoint.save')}
        cancelText={t('pathPoint.cancel')}
      >
        <Form
          form={triggerForm}
          layout="vertical"
          initialValues={{
            type: 'dialogue',
            once: false,
            delay: 0
          }}
        >
          <Form.Item
            name="type"
            label={t('pathPoint.trigger.type')}
            rules={[{ required: true, message: t('pathPoint.trigger.typeRequired') }]}
          >
            <Select placeholder={t('pathPoint.trigger.selectType')}>
              {triggerTypeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item dependencies={['type']}>
            {({ getFieldValue }) => {
              const triggerType = getFieldValue('type');
              return renderTriggerDataEditor(triggerType);
            }}
          </Form.Item>

          <Divider />

          <Form.Item
            name="condition"
            label={t('pathPoint.trigger.condition')}
            tooltip={t('pathPoint.trigger.conditionTooltip')}
          >
            <Input placeholder={t('pathPoint.trigger.conditionPlaceholder')} />
          </Form.Item>

          <Form.Item
            name="delay"
            label={t('pathPoint.trigger.delay')}
            tooltip={t('pathPoint.trigger.delayTooltip')}
          >
            <InputNumber min={0} step={0.1} addonAfter="s" />
          </Form.Item>

          <Form.Item
            name="once"
            label={t('pathPoint.trigger.once')}
            tooltip={t('pathPoint.trigger.onceTooltip')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
