/**
 * 路径跟随组件
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { AvatarPath } from '../path/AvatarPath';
import { PathFollowingOptions, PathEventType, PathEventData } from '../types';
import { EventEmitter } from '../../utils/EventEmitter';

/**
 * 路径跟随状态
 */
export enum PathFollowingState {
  /** 停止 */
  STOPPED = 'stopped',
  /** 运行中 */
  RUNNING = 'running',
  /** 暂停 */
  PAUSED = 'paused',
  /** 等待 */
  WAITING = 'waiting',
  /** 完成 */
  COMPLETED = 'completed'
}

/**
 * 路径跟随组件
 */
export class PathFollowingComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'PathFollowingComponent';

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 目标路径 */
  private path: AvatarPath | null = null;
  /** 当前状态 */
  private state: PathFollowingState = PathFollowingState.STOPPED;
  /** 当前进度 (0-1) */
  private progress: number = 0;
  /** 当前时间 */
  private currentTime: number = 0;
  /** 当前路径点索引 */
  private currentWaypointIndex: number = 0;
  /** 等待时间 */
  private waitTime: number = 0;
  /** 速度倍数 */
  private speedMultiplier: number = 1.0;
  /** 是否循环 */
  private loop: boolean = false;
  /** 是否使用物理 */
  private usePhysics: boolean = false;
  /** 是否启用碰撞检测 */
  private enableCollision: boolean = false;
  /** 当前位置 */
  private currentPosition: THREE.Vector3 = new THREE.Vector3();
  /** 当前旋转 */
  private currentRotation: THREE.Quaternion = new THREE.Quaternion();
  /** 当前速度 */
  private currentSpeed: number = 0;
  /** 当前动画 */
  private currentAnimation: string = '';
  /** 已触发的触发器 */
  private triggeredTriggers: Set<string> = new Set();

  /**
   * 构造函数
   * @param entity 实体
   * @param options 选项
   */
  constructor(entity: Entity, options: Partial<PathFollowingOptions> = {}) {
    super(entity);

    if (options.path) {
      this.setPath(options.path);
    }

    this.loop = options.loop || false;
    this.speedMultiplier = options.speedMultiplier || 1.0;
    this.usePhysics = options.usePhysics || false;
    this.enableCollision = options.enableCollision || false;

    if (options.paused) {
      this.state = PathFollowingState.PAUSED;
    }

    if (options.progress !== undefined) {
      this.setProgress(options.progress);
    }
  }

  /**
   * 设置路径
   * @param path 路径
   */
  public setPath(path: AvatarPath): void {
    this.path = path;
    this.reset();
  }

  /**
   * 获取路径
   * @returns 路径
   */
  public getPath(): AvatarPath | null {
    return this.path;
  }

  /**
   * 开始跟随路径
   */
  public start(): void {
    if (!this.path || this.path.points.length < 2) {
      console.warn('PathFollowingComponent: 无效的路径，无法开始跟随');
      return;
    }

    this.state = PathFollowingState.RUNNING;
    this.emitEvent(PathEventType.PATH_STARTED);
  }

  /**
   * 停止跟随路径
   */
  public stop(): void {
    this.state = PathFollowingState.STOPPED;
    this.reset();
  }

  /**
   * 暂停跟随路径
   */
  public pause(): void {
    if (this.state === PathFollowingState.RUNNING || this.state === PathFollowingState.WAITING) {
      this.state = PathFollowingState.PAUSED;
      this.emitEvent(PathEventType.PATH_PAUSED);
    }
  }

  /**
   * 恢复跟随路径
   */
  public resume(): void {
    if (this.state === PathFollowingState.PAUSED) {
      this.state = PathFollowingState.RUNNING;
      this.emitEvent(PathEventType.PATH_RESUMED);
    }
  }

  /**
   * 重置状态
   */
  public reset(): void {
    this.progress = 0;
    this.currentTime = 0;
    this.currentWaypointIndex = 0;
    this.waitTime = 0;
    this.triggeredTriggers.clear();
    
    if (this.path && this.path.points.length > 0) {
      const firstPoint = this.path.points[0];
      this.currentPosition.copy(firstPoint.position);
      this.currentSpeed = firstPoint.speed;
      this.currentAnimation = firstPoint.animation;
    }
  }

  /**
   * 设置进度
   * @param progress 进度 (0-1)
   */
  public setProgress(progress: number): void {
    if (!this.path) return;

    this.progress = Math.max(0, Math.min(1, progress));
    this.currentTime = this.progress * this.path.totalDuration;
    this.updatePositionFromTime();
  }

  /**
   * 获取当前状态
   * @returns 状态
   */
  public getState(): PathFollowingState {
    return this.state;
  }

  /**
   * 获取当前进度
   * @returns 进度 (0-1)
   */
  public getProgress(): number {
    return this.progress;
  }

  /**
   * 获取当前位置
   * @returns 位置
   */
  public getCurrentPosition(): THREE.Vector3 {
    return this.currentPosition.clone();
  }

  /**
   * 获取当前旋转
   * @returns 旋转
   */
  public getCurrentRotation(): THREE.Quaternion {
    return this.currentRotation.clone();
  }

  /**
   * 获取当前速度
   * @returns 速度
   */
  public getCurrentSpeed(): number {
    return this.currentSpeed;
  }

  /**
   * 获取当前动画
   * @returns 动画名称
   */
  public getCurrentAnimation(): string {
    return this.currentAnimation;
  }

  /**
   * 设置速度倍数
   * @param multiplier 速度倍数
   */
  public setSpeedMultiplier(multiplier: number): void {
    this.speedMultiplier = Math.max(0.1, multiplier);
  }

  /**
   * 获取速度倍数
   * @returns 速度倍数
   */
  public getSpeedMultiplier(): number {
    return this.speedMultiplier;
  }

  /**
   * 设置是否循环
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.loop = loop;
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    if (!this.path || this.state !== PathFollowingState.RUNNING) {
      return;
    }

    // 更新时间
    this.currentTime += deltaTime * this.speedMultiplier;

    // 检查是否完成
    if (this.currentTime >= this.path.totalDuration) {
      if (this.loop) {
        // 循环模式
        this.currentTime = 0;
        this.currentWaypointIndex = 0;
        this.waitTime = 0;
        this.triggeredTriggers.clear();
      } else {
        // 完成
        this.state = PathFollowingState.COMPLETED;
        this.progress = 1.0;
        this.emitEvent(PathEventType.PATH_COMPLETED);
        return;
      }
    }

    // 更新进度
    this.progress = this.path.totalDuration > 0 ? this.currentTime / this.path.totalDuration : 0;

    // 更新位置
    this.updatePositionFromTime();
  }

  /**
   * 根据时间更新位置
   */
  private updatePositionFromTime(): void {
    if (!this.path) return;

    const result = this.path.getPositionAtTime(this.currentTime);
    if (!result) return;

    // 更新位置和旋转
    this.currentPosition.copy(result.position);
    this.currentRotation.copy(result.rotation);
    this.currentSpeed = result.speed;
    this.currentAnimation = result.animation;

    // 检查路径点变化
    if (result.currentPointIndex !== this.currentWaypointIndex) {
      const oldIndex = this.currentWaypointIndex;
      this.currentWaypointIndex = result.currentPointIndex;

      // 触发路径点到达事件
      this.emitEvent(PathEventType.WAYPOINT_REACHED, {
        currentWaypointIndex: this.currentWaypointIndex,
        previousWaypointIndex: oldIndex
      });

      // 处理触发器
      this.processTriggers(this.currentWaypointIndex);
    }

    // 应用变换到实体
    this.applyTransform();
  }

  /**
   * 处理触发器
   * @param pointIndex 路径点索引
   */
  private processTriggers(pointIndex: number): void {
    if (!this.path || pointIndex < 0 || pointIndex >= this.path.points.length) {
      return;
    }

    const point = this.path.points[pointIndex];
    
    point.triggers.forEach((trigger, triggerIndex) => {
      const triggerId = `${pointIndex}_${triggerIndex}`;
      
      // 检查是否已触发（如果设置为只触发一次）
      if (trigger.once && this.triggeredTriggers.has(triggerId)) {
        return;
      }

      // 检查触发条件
      if (trigger.condition && !this.evaluateCondition(trigger.condition)) {
        return;
      }

      // 延迟触发
      if (trigger.delay && trigger.delay > 0) {
        setTimeout(() => {
          this.emitEvent(PathEventType.TRIGGER_ACTIVATED, {
            trigger,
            pointIndex,
            triggerIndex
          });
        }, trigger.delay * 1000);
      } else {
        this.emitEvent(PathEventType.TRIGGER_ACTIVATED, {
          trigger,
          pointIndex,
          triggerIndex
        });
      }

      // 标记为已触发
      if (trigger.once) {
        this.triggeredTriggers.add(triggerId);
      }
    });
  }

  /**
   * 评估触发条件
   * @param condition 条件表达式
   * @returns 是否满足条件
   */
  private evaluateCondition(condition: string): boolean {
    // 简单的条件评估，实际项目中可以使用更复杂的表达式解析器
    try {
      // 这里可以添加更多的上下文变量
      const context = {
        progress: this.progress,
        currentTime: this.currentTime,
        speed: this.currentSpeed,
        waypointIndex: this.currentWaypointIndex
      };

      // 简单的条件检查
      return new Function('context', `with(context) { return ${condition}; }`)(context);
    } catch (error) {
      console.warn('PathFollowingComponent: 条件评估失败', condition, error);
      return true; // 默认为true
    }
  }

  /**
   * 应用变换到实体
   */
  private applyTransform(): void {
    if (!this.entity) return;

    const transform = this.entity.getTransform();
    if (transform) {
      transform.setPosition(this.currentPosition.x, this.currentPosition.y, this.currentPosition.z);
      transform.setRotationFromQuaternion(this.currentRotation);
    }
  }

  /**
   * 发射事件
   * @param type 事件类型
   * @param data 额外数据
   */
  private emitEvent(type: PathEventType, data: any = {}): void {
    const eventData: PathEventData = {
      type,
      pathId: this.path?.id || '',
      entityId: this.entity?.id || '',
      currentWaypointIndex: this.currentWaypointIndex,
      progress: this.progress,
      timestamp: Date.now(),
      ...data
    };

    this.eventEmitter.emit(type, eventData);
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public on(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public off(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    this.stop();
    this.eventEmitter.removeAllListeners();
    super.destroy();
  }
}
