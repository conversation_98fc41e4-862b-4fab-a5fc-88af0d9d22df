/**
 * 路径属性面板组件
 */
import React, { useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  InputNumber,
  Card,
  Space,
  Tag,
  Divider,
  Descriptions,
  Progress,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  InfoCircleOutlined,
  ClockCircleOutlined,
  RocketOutlined,
  NodeIndexOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 路径数据接口
 */
interface PathData {
  id: string;
  name: string;
  avatarId: string;
  points: any[];
  loopMode: 'none' | 'loop' | 'pingpong';
  interpolation: 'linear' | 'smooth' | 'bezier' | 'spline';
  enabled: boolean;
  totalDuration: number;
  metadata: {
    createdAt: string;
    updatedAt: string;
    creator: string;
    version: number;
    description?: string;
    tags?: string[];
  };
}

/**
 * 属性面板属性
 */
interface PathPropertiesPanelProps {
  /** 路径数据 */
  path: PathData;
  /** 是否只读 */
  readonly?: boolean;
  /** 变化回调 */
  onChange?: (values: Partial<PathData>) => void;
}

/**
 * 路径属性面板组件
 */
export const PathPropertiesPanel: React.FC<PathPropertiesPanelProps> = ({
  path,
  readonly = false,
  onChange
}) => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 循环模式选项
  const loopModeOptions = [
    { value: 'none', label: t('pathProperties.loopMode.none') },
    { value: 'loop', label: t('pathProperties.loopMode.loop') },
    { value: 'pingpong', label: t('pathProperties.loopMode.pingpong') }
  ];

  // 插值类型选项
  const interpolationOptions = [
    { value: 'linear', label: t('pathProperties.interpolation.linear') },
    { value: 'smooth', label: t('pathProperties.interpolation.smooth') },
    { value: 'bezier', label: t('pathProperties.interpolation.bezier') },
    { value: 'spline', label: t('pathProperties.interpolation.spline') }
  ];

  /**
   * 处理表单值变化
   */
  const handleFormChange = (changedValues: any, allValues: any) => {
    if (readonly) return;
    onChange?.(allValues);
  };

  /**
   * 计算路径统计信息
   */
  const calculatePathStats = () => {
    if (!path.points || path.points.length === 0) {
      return {
        totalLength: 0,
        averageSpeed: 0,
        totalWaitTime: 0,
        estimatedDuration: 0
      };
    }

    let totalLength = 0;
    let totalSpeed = 0;
    let totalWaitTime = 0;

    // 计算路径长度和统计信息
    for (let i = 0; i < path.points.length; i++) {
      const point = path.points[i];
      totalSpeed += point.speed || 1;
      totalWaitTime += point.waitTime || 0;

      if (i < path.points.length - 1) {
        const nextPoint = path.points[i + 1];
        const dx = nextPoint.position.x - point.position.x;
        const dy = nextPoint.position.y - point.position.y;
        const dz = nextPoint.position.z - point.position.z;
        const distance = Math.sqrt(dx * dx + dy * dy + dz * dz);
        totalLength += distance;
      }
    }

    const averageSpeed = path.points.length > 0 ? totalSpeed / path.points.length : 0;
    const estimatedDuration = averageSpeed > 0 ? totalLength / averageSpeed + totalWaitTime : 0;

    return {
      totalLength,
      averageSpeed,
      totalWaitTime,
      estimatedDuration
    };
  };

  /**
   * 获取路径复杂度
   */
  const getPathComplexity = () => {
    if (!path.points || path.points.length < 3) return 0;

    let totalAngleChange = 0;
    let totalSpeedChange = 0;

    for (let i = 1; i < path.points.length - 1; i++) {
      const prev = path.points[i - 1];
      const curr = path.points[i];
      const next = path.points[i + 1];

      // 计算方向变化
      const v1 = {
        x: curr.position.x - prev.position.x,
        z: curr.position.z - prev.position.z
      };
      const v2 = {
        x: next.position.x - curr.position.x,
        z: next.position.z - curr.position.z
      };

      const len1 = Math.sqrt(v1.x * v1.x + v1.z * v1.z);
      const len2 = Math.sqrt(v2.x * v2.x + v2.z * v2.z);

      if (len1 > 0 && len2 > 0) {
        const dot = (v1.x * v2.x + v1.z * v2.z) / (len1 * len2);
        const angle = Math.acos(Math.max(-1, Math.min(1, dot)));
        totalAngleChange += angle;
      }

      // 计算速度变化
      totalSpeedChange += Math.abs((curr.speed || 1) - (prev.speed || 1));
    }

    // 归一化复杂度 (0-100)
    const angleComplexity = Math.min(totalAngleChange / (Math.PI * path.points.length), 1) * 50;
    const speedComplexity = Math.min(totalSpeedChange / (10 * path.points.length), 1) * 50;

    return Math.round(angleComplexity + speedComplexity);
  };

  /**
   * 格式化时间
   */
  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 初始化表单
  useEffect(() => {
    form.setFieldsValue(path);
  }, [path, form]);

  const stats = calculatePathStats();
  const complexity = getPathComplexity();

  return (
    <div className="path-properties-panel">
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        disabled={readonly}
      >
        {/* 基本信息 */}
        <Card title={t('pathProperties.basicInfo')} size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name="name"
            label={t('pathProperties.name')}
            rules={[{ required: true, message: t('pathProperties.nameRequired') }]}
          >
            <Input placeholder={t('pathProperties.namePlaceholder')} />
          </Form.Item>

          <Form.Item
            name="avatarId"
            label={t('pathProperties.avatarId')}
            tooltip={t('pathProperties.avatarIdTooltip')}
          >
            <Input placeholder={t('pathProperties.avatarIdPlaceholder')} />
          </Form.Item>

          <Form.Item
            name={['metadata', 'description']}
            label={t('pathProperties.description')}
          >
            <TextArea 
              rows={3} 
              placeholder={t('pathProperties.descriptionPlaceholder')} 
            />
          </Form.Item>

          <Form.Item
            name={['metadata', 'tags']}
            label={t('pathProperties.tags')}
          >
            <Select
              mode="tags"
              placeholder={t('pathProperties.tagsPlaceholder')}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name="enabled"
            label={t('pathProperties.enabled')}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Card>

        {/* 路径设置 */}
        <Card title={t('pathProperties.pathSettings')} size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            name="loopMode"
            label={t('pathProperties.loopMode.title')}
            tooltip={t('pathProperties.loopMode.tooltip')}
          >
            <Select placeholder={t('pathProperties.loopMode.placeholder')}>
              {loopModeOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="interpolation"
            label={t('pathProperties.interpolation.title')}
            tooltip={t('pathProperties.interpolation.tooltip')}
          >
            <Select placeholder={t('pathProperties.interpolation.placeholder')}>
              {interpolationOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Card>

        {/* 统计信息 */}
        <Card title={t('pathProperties.statistics')} size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Statistic
                title={t('pathProperties.stats.points')}
                value={path.points?.length || 0}
                prefix={<NodeIndexOutlined />}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title={t('pathProperties.stats.length')}
                value={stats.totalLength}
                precision={2}
                suffix="m"
                prefix={<RocketOutlined />}
              />
            </Col>
          </Row>

          <Divider />

          <Row gutter={16}>
            <Col span={12}>
              <Statistic
                title={t('pathProperties.stats.duration')}
                value={formatDuration(stats.estimatedDuration)}
                prefix={<ClockCircleOutlined />}
              />
            </Col>
            <Col span={12}>
              <Statistic
                title={t('pathProperties.stats.avgSpeed')}
                value={stats.averageSpeed}
                precision={2}
                suffix="m/s"
              />
            </Col>
          </Row>

          <Divider />

          <div style={{ marginBottom: 16 }}>
            <div style={{ marginBottom: 8 }}>
              <span>{t('pathProperties.stats.complexity')}: </span>
              <Tag color={complexity < 30 ? 'green' : complexity < 70 ? 'orange' : 'red'}>
                {complexity}%
              </Tag>
            </div>
            <Progress
              percent={complexity}
              strokeColor={complexity < 30 ? '#52c41a' : complexity < 70 ? '#faad14' : '#ff4d4f'}
              showInfo={false}
            />
          </div>

          <Descriptions size="small" column={1}>
            <Descriptions.Item label={t('pathProperties.stats.waitTime')}>
              {formatDuration(stats.totalWaitTime)}
            </Descriptions.Item>
            <Descriptions.Item label={t('pathProperties.stats.triggers')}>
              {path.points?.reduce((sum, point) => sum + (point.triggers?.length || 0), 0) || 0}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        {/* 元数据 */}
        <Card title={t('pathProperties.metadata')} size="small">
          <Descriptions size="small" column={1}>
            <Descriptions.Item label={t('pathProperties.id')}>
              {path.id}
            </Descriptions.Item>
            <Descriptions.Item label={t('pathProperties.version')}>
              v{path.metadata?.version || 1}
            </Descriptions.Item>
            <Descriptions.Item label={t('pathProperties.creator')}>
              {path.metadata?.creator || 'Unknown'}
            </Descriptions.Item>
            <Descriptions.Item label={t('pathProperties.created')}>
              {path.metadata?.createdAt ? new Date(path.metadata.createdAt).toLocaleString() : '-'}
            </Descriptions.Item>
            <Descriptions.Item label={t('pathProperties.updated')}>
              {path.metadata?.updatedAt ? new Date(path.metadata.updatedAt).toLocaleString() : '-'}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      </Form>
    </div>
  );
};
